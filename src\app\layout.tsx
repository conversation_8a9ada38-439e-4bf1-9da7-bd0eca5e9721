import { Metadata } from 'next';

import './globals.css';
import '@/styles/v2.css';

import { VersionProvider } from '@/lib/version-context';

export const metadata: Metadata = {
  title: 'GuanHua Chen Group - Theoretical and Computational Chemistry',
  description: 'GuanHua Chen Group - Theoretical and Computational Chemistry at The University of Hong Kong',
  icons: {
    icon: '/favicon.ico',
    shortcut: '/favicon.ico',
    apple: '/favicon.ico',
  },
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <body>
        <VersionProvider>{children}</VersionProvider>
      </body>
    </html>
  );
}

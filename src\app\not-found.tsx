import Link from 'next/link';
import { useEffect, useState } from 'react';

export default function NotFound() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  return (
    <div
      className="min-h-screen flex items-center justify-center bg-[#303030] text-[#D8D8D8] p-4"
      style={{ backgroundImage: "url('/v2/img/background/body.jpg')" }}
    >
      <div className="max-w-2xl w-full text-center">
        {/* 404 Text with gradient effect */}
        <div className="mb-8">
          <h1 className="text-[150px] font-bold text-transparent bg-clip-text bg-gradient-to-r from-[#FBB917] to-[#FAAC58] leading-none mb-4">
            404
          </h1>
          <div className="h-1 w-32 bg-gradient-to-r from-[#FBB917] to-[#FAAC58] mx-auto mb-6"></div>
        </div>

        {/* Main content */}
        <div className="bg-[#303030] bg-opacity-90 p-8 rounded-lg border border-[#FBB917] border-opacity-30 backdrop-blur-sm">
          <h2 className="text-3xl font-bold text-[#FFE87C] mb-4">Page Not Found</h2>
          <p className="text-[#D8D8D8] mb-8 text-lg">
            Oops! The page you're looking for seems to have wandered off into the digital wilderness.
          </p>

          {/* Navigation options */}
          <div className="space-y-6">
            {/* Primary CTA */}
            <Link
              href="/"
              className="inline-block bg-gradient-to-r from-[#FBB917] to-[#FAAC58] text-[#303030] px-8 py-3 rounded-lg font-bold text-lg hover:shadow-lg transform hover:scale-105 transition-all duration-300"
            >
              Return to Home
            </Link>

            {/* Version selection */}
            <div className="pt-6 border-t border-[#FBB917] border-opacity-20">
              <p className="text-[#FFE87C] mb-4">Choose your preferred version:</p>
              <div className="flex gap-6 justify-center">
                <Link
                  href="/v1/home"
                  className="group px-6 py-3 border border-[#FBB917] rounded-lg hover:bg-[#FBB917] hover:bg-opacity-10 transition-all duration-300"
                >
                  <span className="text-[#FAAC58] group-hover:text-[#FFE87C] font-semibold">Version 1.0</span>
                </Link>
                <Link
                  href="/v2/home"
                  className="group px-6 py-3 border border-[#FBB917] rounded-lg hover:bg-[#FBB917] hover:bg-opacity-10 transition-all duration-300"
                >
                  <span className="text-[#FAAC58] group-hover:text-[#FFE87C] font-semibold">Version 2.0</span>
                </Link>
              </div>
            </div>

            {/* Additional help */}
            <div className="text-sm text-[#D8D8D8] opacity-70 mt-8">
              <p>If you believe this is an error, please contact the site administrator.</p>
            </div>
          </div>
        </div>

        {/* Animated elements */}
        <div className="mt-12 flex justify-center space-x-4">
          {[...Array(3)].map((_, i) => (
            <div
              key={i}
              className={`w-2 h-2 bg-[#FBB917] rounded-full ${mounted ? 'animate-pulse' : ''}`}
              style={{
                animationDelay: `${i * 0.2}s`,
                opacity: 0.6 - i * 0.2,
              }}
            ></div>
          ))}
        </div>
      </div>
    </div>
  );
}

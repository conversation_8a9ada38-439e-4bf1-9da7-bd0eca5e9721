---
type: "always_apply"
---

# 通用代码项目功能映射生成器

## 🎯 目标
为任意代码项目生成一份**AI可理解的功能索引**，让用户能够：
- 用自然语言描述想修改的功能
- AI能快速定位到具体的代码文件和位置
- 实现"指哪打哪"的精确代码修改

## 📋 核心任务
扫描项目代码，识别所有**用户可感知的功能点**，并建立**自然语言到代码位置**的映射关系。

## 🔍 分析要点

### 识别标准
1. **界面元素**: 按钮、表单、菜单、弹窗等
2. **交互行为**: 点击、拖拽、输入、导航等
3. **数据展示**: 列表、图表、卡片、详情等
4. **业务流程**: 登录、支付、搜索、上传等

### AI友好的描述原则
- 使用**用户视角**的自然语言
- 避免技术术语，多用功能性描述
- 包含**视觉特征**和**交互方式**
- 提供**多种表达方式**的关键词

## 📊 输出格式

```markdown
# 📂 [项目名称] 功能-代码映射报告

## 🏗️ 项目概览
- **技术栈**: [主要框架] + [其他重要依赖]
- **架构模式**: [识别的架构模式，如 MVC, Component-based, Feature-based 等]
- **状态管理**: [使用的状态管理方案，如 Redux, Context, Vuex 等]
- **样式方案**: [CSS Modules/Styled-components/Tailwind/SCSS 等]
- **构建工具**: [Webpack, Vite, Create React App 等]
- **包管理**: [npm, yarn, pnpm]

## 📊 功能模块统计
- **页面级组件**: X 个 [主要页面/路由]
- **可复用组件**: Y 个 [通用UI组件]
- **业务逻辑模块**: Z 个 [Hooks/Services/Utils]
- **样式文件**: W 个 [CSS/SCSS/样式模块]
- **配置文件**: N 个 [路由/环境/构建配置]

## 🗂️ 目录结构概览
```
[项目根目录]/
├── [主要源码目录]/
│   ├── [页面目录]/
│   ├── [组件目录]/
│   ├── [样式目录]/
│   └── [工具目录]/
├── [资源目录]/
└── [配置文件]
```

---

## 🎯 功能映射表

### [功能类别] - [功能名称]

**🔤 用户描述方式**:
- 主要: "[用户会怎么描述这个功能]"
- 别名: "[其他可能的叫法]", "[相关词汇]"

**📍 代码位置**:
- 主文件: `[文件路径]` - [作用说明]
- 样式: `[样式文件路径]` (如果有)
- 逻辑: `[业务逻辑文件]` (如果分离)

**🎨 视觉标识**:
- 外观: [颜色/形状/位置等描述]
- 文本: "[界面上的关键文字]"

**⚡ 修改指引**:
- 修改外观: 编辑 `[具体文件和行数/区域]`
- 修改行为: 编辑 `[具体文件和函数名]`
- 修改文本: 编辑 `[具体位置]`

---
```

## 📝 示例模板

```markdown
### 界面元素 - 用户头像

**🔤 用户描述方式**:
- 主要: "用户头像", "个人头像", "用户图片"
- 别名: "头像框", "用户照片", "个人图标", "profile picture"

**📍 代码位置**:
- 主文件: `src/components/UserAvatar.jsx` - 头像组件定义
- 样式: `src/components/UserAvatar.css` - 头像圆形边框和大小
- 默认图片: `public/images/default-avatar.png`

**🎨 视觉标识**:
- 外观: 圆形图片，通常在右上角或用户信息区域
- 文本: 无文本，但可能有用户名在旁边

**⚡ 修改指引**:
- 修改头像大小: 编辑 `UserAvatar.css` 中的 `width/height`
- 修改默认头像: 替换 `public/images/default-avatar.png`
- 修改点击行为: 编辑 `UserAvatar.jsx` 中的 `onClick` 函数

---

### 交互功能 - 商品收藏

**🔤 用户描述方式**:
- 主要: "收藏商品", "添加到收藏夹", "收藏按钮"
- 别名: "点赞", "喜欢", "心形按钮", "收藏功能", "favorite"

**📍 代码位置**:
- 主文件: `src/components/FavoriteButton.jsx` - 收藏按钮组件
- 业务逻辑: `src/services/favoriteService.js` - 收藏相关API调用
- 状态管理: `src/store/userStore.js` - 收藏列表状态

**🎨 视觉标识**:
- 外观: 心形图标，未收藏时为空心，已收藏时为红色实心
- 位置: 通常在商品卡片右上角或商品详情页

**⚡ 修改指引**:
- 修改图标样式: 编辑 `FavoriteButton.jsx` 中的图标组件
- 修改收藏逻辑: 编辑 `favoriteService.js` 中的收藏方法
- 修改收藏状态: 编辑 `userStore.js` 中的收藏数据结构

---
```

## 🚀 使用说明

### 对于用户
当你想修改某个功能时，只需告诉AI：
- "我想修改登录按钮的颜色"
- "搜索框的位置需要调整"
- "商品评分的星星太小了"

### 对于AI
收到用户需求后：
1. 在映射表中搜索相关的"用户描述方式"
2. 定位到对应的"代码位置"
3. 根据"修改指引"提供具体的修改方案

## 🎯 生成指令

请分析提供的项目代码，按照上述格式生成功能映射表。重点关注：

1. **完整性**: 覆盖所有用户可见的功能
2. **准确性**: 确保代码位置和修改指引正确
3. **自然性**: 用户描述要贴近真实表达习惯
4. **实用性**: AI能够直接根据映射表定位和修改代码

现在请开始分析我的项目代码，生成这份AI友好的功能映射表。

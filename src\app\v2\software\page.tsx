import Link from 'next/link';

import BaseLayout from '@/components/v2/layouts/BaseLayout';

export default function V2SoftwarePage() {
  return (
    <BaseLayout headerType="header6" contentType="content6" showSidebar={false}>
      <div style={{ width: '800px' }}>
        <div style={{ width: '720px' }}>
          <h2>LODESTAR v1.0</h2>
          <p style={{ textAlign: 'center' }}>
            {/* LODESTAR 图片占位符 - 如果有实际图片，请替换 src */}
            <div
              style={{
                width: '200px',
                height: '100px',
                backgroundColor: '#555',
                border: '1px solid #FBB917',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                margin: '0 auto',
                color: '#FBB917',
                fontSize: '14px',
              }}
            >
              LODESTAR v1.0
            </div>
            <br />
            LODESTAR v1.0 is a linear-scaling quantum chemistry program based on the localized-density-matrix (LDM)
            method developed at University of Hong Kong.
            <br />
            <br />
            <Link
              href="http://yangtze.hku.hk/LODESTAR/download/"
              className="text-[#FAAC58] hover:text-[#FF7F00]"
              target="_blank"
              rel="noopener noreferrer"
            >
              &gt;&gt;&gt; DOWNLOAD &lt;&lt;&lt;
            </Link>
            <br />
            <br />
            <Link
              href="http://yangtze.hku.hk/discussion/reqdpost.htm"
              className="text-[#FAAC58] hover:text-[#FF7F00]"
              target="_blank"
              rel="noopener noreferrer"
            >
              GIVE SUGGESTIONS
            </Link>
            <br />
            <Link href="mailto:<EMAIL>" className="text-[#FAAC58] hover:text-[#FF7F00]">
              ASK US FOR HELP
            </Link>
            <br />
            <br />
            <br />
          </p>

          <h2>For Developers</h2>
          <p style={{ textAlign: 'center' }}>
            <Link
              href="http://yangtze.hku.hk/gwiki"
              className="text-[#FAAC58] hover:text-[#FF7F00]"
              target="_blank"
              rel="noopener noreferrer"
            >
              WIKI pages for internal discussions and documentations
            </Link>
            <br />
            <br />
            <br />
            <br />
            <br />
            <br />
            <br />
          </p>
        </div>
      </div>
    </BaseLayout>
  );
}

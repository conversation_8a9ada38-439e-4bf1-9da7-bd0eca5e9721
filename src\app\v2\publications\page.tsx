import Footer from '@/components/v2/Footer';
import BaseLayout from '@/components/v2/layouts/BaseLayout';
import { publicationData } from '@/db/v2/publications';

export default function V2PublicationsPage() {
  return (
    <>
      <BaseLayout headerType="header3" contentType="content3" showSidebar={false}>
        <h2>Full publication list</h2> &nbsp;&nbsp;&nbsp; (in reverse order) <br />
        <p align="justify">
          {publicationData.publications.map((pub) => (
            <span key={pub.id}>
              <br /> {pub.id}. "{pub.title}", {pub.authors},{' '}
              {pub.doi ? (
                <a href={pub.doi}>
                  {pub.journal} {pub.year}, {pub.volume}, {pub.pages}
                </a>
              ) : (
                <span>
                  {pub.journal} {pub.year}, {pub.volume}, {pub.pages}
                </span>
              )}{' '}
              {pub.pdfLink && <a href={pub.pdfLink}>(PDF)</a>}
              <br />
            </span>
          ))}
        </p>
        <br />
        <p>
          <i>... and {publicationData.totalCount - publicationData.publications.length} more publications</i>
        </p>
      </BaseLayout>
    </>
  );
}

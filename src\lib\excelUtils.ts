import * as XLSX from 'xlsx';

export interface PaperData {
  id: number;
  title: string;
  authors: string;
  doi?: string;
  journal: string;
  year: string;
  volume: string;
  pages: string;
  hasPdf?: boolean;
}

export async function readPapersFromExcel(): Promise<PaperData[]> {
  try {
    // 在 Next.js 中，public 文件夹下的文件可以通过根路径访问
    const response = await fetch('/v2/doc/papers.xlsx');
    if (!response.ok) {
      throw new Error('Failed to fetch papers.xlsx');
    }

    const arrayBuffer = await response.arrayBuffer();
    const workbook = XLSX.read(arrayBuffer, { type: 'array' });

    // 获取第一个工作表
    const worksheetNames = workbook.SheetNames;
    if (!worksheetNames || worksheetNames.length === 0) {
      return [];
    }
    const worksheetName = worksheetNames[0];
    const worksheet = workbook.Sheets[worksheetName as string];

    if (!worksheet) {
      return [];
    }

    // 将工作表转换为 JSON
    const jsonData = XLSX.utils.sheet_to_json(worksheet);

    // 转换数据格式，保持原始顺序
    const processedData = jsonData.map((row: any, index) => {
      const getValue = (keys: string[]): string => {
        for (const key of keys) {
          if (row[key] !== undefined && row[key] !== null) {
            return String(row[key]);
          }
        }
        return '';
      };

      const getOptionalValue = (keys: string[]): string | undefined => {
        for (const key of keys) {
          if (row[key] !== undefined && row[key] !== null) {
            return String(row[key]);
          }
        }
        return undefined;
      };

      return {
        id: index + 1,
        title: getValue(['title', 'Title']),
        authors: getValue(['authors', 'Authors']),
        doi: getOptionalValue(['doi', 'DOI']),
        journal: getValue(['journal', 'Journal']),
        year: getValue(['year', 'Year']),
        volume: getValue(['volume', 'Volume']),
        pages: getValue(['pages', 'Pages']),
        hasPdf: Boolean(row.hasPdf || row.HasPdf),
      };
    });

    // 返回处理后的数据
    return processedData;
  } catch (error) {
    console.error('Error reading papers.xlsx:', error);
    return [];
  }
}
